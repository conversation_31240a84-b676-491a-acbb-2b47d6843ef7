#include "bsp_system.h"

typedef struct{
	void (*task_func)(void);
	uint32_t run_time;
	uint32_t last_time;
} task_t;


static task_t task_list[] = 
{
	{uart1_task, 20, 0},

};


#define TASK_NUM_MAX      ( sizeof(task_list) / sizeof(task_list[0]) )

__IO uint16_t Task_Num_Max_text =  ( sizeof(task_list) / sizeof(task_list[0]) );
__IO uint16_t Task_t_Num_Max_text = sizeof(task_list);
__IO uint16_t Task_list_Num_Max_text = sizeof(task_list[0]);

void scheduler_run(void)
{
	for (uint8_t i = 0; i < TASK_NUM_MAX; i++)
	{
		uint32_t now_time = HAL_GetTick();
		if (now_time >= task_list[i].last_time + task_list[i].run_time)
		{
			task_list[i].last_time = now_time;
			task_list[i].task_func();
		}
	}
}


