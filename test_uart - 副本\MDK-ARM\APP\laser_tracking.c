#include "laser_tracking.h"
#include <math.h>

// 全局变量定义
tracking_data_t tracking_data = {0};
pid_controller_t pid_x = {0}, pid_y = {0};
point_t current_laser_pos = {0};
bool laser_data_ready = false;
bool path_completed = false;

// 当前目标路径点索引
static uint16_t current_target_index = 0;
static bool tracking_active = false;

// 舵机当前角度状态（用于增量控制）
static float current_servo_x_angle = 135.0f;  // X轴舵机当前角度（初始中位）
static float current_servo_y_angle = 90.0f;   // Y轴舵机当前角度（初始中位）

// 坐标范围（根据K230输出调整）
#define COORD_X_MIN 0
#define COORD_X_MAX 640
#define COORD_Y_MIN 0
#define COORD_Y_MAX 480

// 舵机角度范围
#define SERVO_X_MIN 0.0f
#define SERVO_X_MAX 270.0f
#define SERVO_Y_MIN 0.0f
#define SERVO_Y_MAX 180.0f

/**
 * @brief 初始化激光循迹系统
 */
void laser_tracking_init(void)
{
    // 初始化PID控制器
    pid_init(&pid_x, PID_X_KP, PID_X_KI, PID_X_KD);
    pid_init(&pid_y, PID_Y_KP, PID_Y_KI, PID_Y_KD);
    
    // 初始化跟踪数据
    tracking_data.path_ready = false;
    tracking_data.path_count = 0;
    current_target_index = 0;
    tracking_active = false;
    laser_data_ready = false;
    path_completed = false;

    // 初始化舵机角度状态
    current_servo_x_angle = 135.0f;  // X轴中位
    current_servo_y_angle = 90.0f;   // Y轴中位
    
    printf("=== Laser Tracking System Initialized ===\r\n");
    printf("PID Parameters - X-axis: Kp=%.2f, Ki=%.2f, Kd=%.2f\r\n", PID_X_KP, PID_X_KI, PID_X_KD);
    printf("PID Parameters - Y-axis: Kp=%.2f, Ki=%.2f, Kd=%.2f\r\n", PID_Y_KP, PID_Y_KI, PID_Y_KD);
    printf("Coordinate Range: X(0-%d), Y(0-%d)\r\n", COORD_X_MAX, COORD_Y_MAX);
    printf("Servo Angle Range: X(%.1f-%.1f), Y(%.1f-%.1f)\r\n", SERVO_X_MIN, SERVO_X_MAX, SERVO_Y_MIN, SERVO_Y_MAX);
    printf("System initialized, waiting for corner coordinates...\r\n");
    printf("==========================================\r\n");
}

/**
 * @brief 初始化PID控制器
 */
void pid_init(pid_controller_t* pid, float kp, float ki, float kd)
{
    pid->kp = kp;
    pid->ki = ki;
    pid->kd = kd;
    pid->error = 0.0f;
    pid->last_error = 0.0f;
    pid->integral = 0.0f;
    pid->output = 0.0f;
}

/**
 * @brief PID计算
 */
float pid_calculate(pid_controller_t* pid, float setpoint, float feedback)
{
    pid->error = setpoint - feedback;
    pid->integral += pid->error;
    
    // 积分限幅
    if (pid->integral > 100.0f) pid->integral = 100.0f;
    if (pid->integral < -100.0f) pid->integral = -100.0f;
    
    pid->output = pid->kp * pid->error + 
                  pid->ki * pid->integral + 
                  pid->kd * (pid->error - pid->last_error);
    
    pid->last_error = pid->error;
    
    return pid->output;
}

/**
 * @brief 检查是否为十六进制字符串格式
 */
bool is_hex_string_format(uint8_t* data, uint16_t size)
{
    // 检查是否包含十六进制字符和空格
    for (int i = 0; i < size; i++) {
        uint8_t c = data[i];
        if (!((c >= '0' && c <= '9') ||
              (c >= 'A' && c <= 'F') ||
              (c >= 'a' && c <= 'f') ||
              c == ' ' || c == '\r' || c == '\n')) {
            return false;
        }
    }
    return true;
}

/**
 * @brief 处理接收到的数据
 */
void process_received_data(uint8_t* data, uint16_t size)
{
    printf("Received data packet: size=%d\r\n", size);
    printf("Data: ");
    for (int i = 0; i < size && i < 20; i++) {
        printf("%02X ", data[i]);
    }
    printf("\r\n");

    if (size < 6) {
        printf("Packet too short\r\n");
        return;  // 最小数据包长度
    }

    // 检查是否是ASCII格式的数据包（以"0x"开头）
    if (data[0] == '0' && data[1] == 'x') {
        printf("Detected 0x-prefixed ASCII format packet\r\n");
        process_ascii_packet(data, size);
        return;
    }

    // 检查是否是纯十六进制字符串格式（如"AA 00 4E..."）
    if (is_hex_string_format(data, size)) {
        printf("Detected hex string format packet\r\n");
        process_hex_string_packet(data, size);
        return;
    }

    // 检查帧头（二进制格式）
    if (data[0] != FRAME_HEADER) {
        printf("Invalid frame header: %02X\r\n", data[0]);
        return;
    }

    // 根据数据包长度判断类型（二进制格式）
    if (size == CORNERS_PACKET_LEN && data[17] == FRAME_TAIL) {
        printf("Processing corners packet\r\n");
        // 角点坐标包
        process_corners_packet(data);
    }
    else if (size == LASER_PACKET_LEN && data[5] == FRAME_TAIL) {
        printf("Processing laser packet\r\n");
        // 激光点坐标包
        process_laser_packet(data);
    }
    else {
        printf("Unknown packet type: size=%d, tail=%02X\r\n", size, data[size-1]);
    }
}

/**
 * @brief 处理角点坐标包
 */
void process_corners_packet(uint8_t* data)
{
    printf("=== Processing Corner Coordinates ===\r\n");
    
    // 解析4个角点坐标（跳过帧头0xAA，从索引1开始）
    for (int i = 0; i < 4; i++) {
        uint16_t x = (data[2*i+1] << 8) | data[2*i+2];  // 小端格式：低字节在前
        uint16_t y = (data[2*i+3] << 8) | data[2*i+4];
        
        tracking_data.corners[i].x = x;
        tracking_data.corners[i].y = y;
        
        printf("Corner %d: (0x%04X, 0x%04X) = (%d, %d)\r\n", i+1, x, y, x, y);
    }
    
    // 生成矩形路径
    generate_rectangle_path();
    
    // 重置跟踪状态
    current_target_index = 0;
    tracking_active = false;
    path_completed = false;
    
    printf("=== Corner Coordinates Received Successfully ===\r\n");
    printf("Corner 1: (0x%04X, 0x%04X) = (%d, %d)\r\n", 
           tracking_data.corners[0].x, tracking_data.corners[0].y,
           tracking_data.corners[0].x, tracking_data.corners[0].y);
    printf("Corner 2: (0x%04X, 0x%04X) = (%d, %d)\r\n", 
           tracking_data.corners[1].x, tracking_data.corners[1].y,
           tracking_data.corners[1].x, tracking_data.corners[1].y);
    printf("Corner 3: (0x%04X, 0x%04X) = (%d, %d)\r\n", 
           tracking_data.corners[2].x, tracking_data.corners[2].y,
           tracking_data.corners[2].x, tracking_data.corners[2].y);
    printf("Corner 4: (0x%04X, 0x%04X) = (%d, %d)\r\n", 
           tracking_data.corners[3].x, tracking_data.corners[3].y,
           tracking_data.corners[3].x, tracking_data.corners[3].y);
    printf("Rectangle path generated, waiting for laser point coordinates...\r\n");
    printf("==============================================\r\n");
}

/**
 * @brief 处理激光点坐标包
 */
void process_laser_packet(uint8_t* data)
{
    // 解析激光点坐标（跳过帧头0xAA，从索引1开始）
    uint16_t x = (data[1] << 8) | data[2];  // 小端格式：低字节在前
    uint16_t y = (data[3] << 8) | data[4];
    
    update_laser_position(x, y);
    
    printf("=== Laser Point Coordinates Received ===\r\n");
    printf("Laser point position: (0x%04X, 0x%04X) = (%d, %d)\r\n", x, y, x, y);
    printf("=====================================\r\n");
}

/**
 * @brief 更新激光点位置
 */
void update_laser_position(uint16_t x, uint16_t y)
{
    // 检查坐标范围
    if (x > COORD_X_MAX || y > COORD_Y_MAX) {
        printf("Warning: Coordinates out of range!\r\n");
        printf("X: 0x%04X (%d) > max: 0x%04X (%d)\r\n", x, x, COORD_X_MAX, COORD_X_MAX);
        printf("Y: 0x%04X (%d) > max: 0x%04X (%d)\r\n", y, y, COORD_Y_MAX, COORD_Y_MAX);
        // 限制坐标范围
        if (x > COORD_X_MAX) x = COORD_X_MAX;
        if (y > COORD_Y_MAX) y = COORD_Y_MAX;
    }
    
    current_laser_pos.x = x;
    current_laser_pos.y = y;
    laser_data_ready = true;
    
    // 如果路径已生成但还未开始跟踪，自动开始跟踪
    if (tracking_data.path_ready && !tracking_active && !path_completed) {
        tracking_active = true;
        current_target_index = 0;
        printf("=== Auto Start Laser Tracking ===\r\n");
        printf("Laser point detected, starting auto tracking...\r\n");
        printf("Path ready: %s\r\n", tracking_data.path_ready ? "Yes" : "No");
        printf("Tracking active: %s\r\n", tracking_active ? "Yes" : "No");
        printf("Path completed: %s\r\n", path_completed ? "Yes" : "No");
        printf("================================\r\n");
    }
    
    // 如果正在跟踪且路径未完成，执行跟踪控制
    if (tracking_data.path_ready && tracking_active && !path_completed) {
        printf("Executing tracking control...\r\n");
        tracking_control();
    } else {
        printf("Skipping tracking control - Path ready: %s, Active: %s, Completed: %s\r\n",
               tracking_data.path_ready ? "Yes" : "No",
               tracking_active ? "Yes" : "No",
               path_completed ? "Yes" : "No");
    }
}

/**
 * @brief 生成矩形路径
 */
void generate_rectangle_path(void)
{
    tracking_data.path_count = 0;

    // 计算矩形边界
    uint16_t min_x = tracking_data.corners[0].x;
    uint16_t max_x = tracking_data.corners[0].x;
    uint16_t min_y = tracking_data.corners[0].y;
    uint16_t max_y = tracking_data.corners[0].y;

    for (int i = 1; i < 4; i++) {
        if (tracking_data.corners[i].x < min_x) min_x = tracking_data.corners[i].x;
        if (tracking_data.corners[i].x > max_x) max_x = tracking_data.corners[i].x;
        if (tracking_data.corners[i].y < min_y) min_y = tracking_data.corners[i].y;
        if (tracking_data.corners[i].y > max_y) max_y = tracking_data.corners[i].y;
    }

    printf("Rectangle bounds: X(%d-%d), Y(%d-%d)\r\n", min_x, max_x, min_y, max_y);

    // 生成矩形路径点（按照左下->左上->右上->右下->左下的顺序）
    uint16_t index = 0;
    uint16_t step = 10;  // 增大步长，减少路径点数量

    // 1. 左边（从下到上）：左下 -> 左上
    for (uint16_t y = max_y; y >= min_y && index < MAX_PATH_POINTS; y -= step) {
        tracking_data.path_points[index].x = min_x;
        tracking_data.path_points[index].y = y;
        printf("Path point %d: (%d, %d) - Left edge\r\n", index, min_x, y);
        index++;
        if (y <= min_y + step) break;  // 防止无限循环
    }

    // 2. 上边（从左到右）：左上 -> 右上
    for (uint16_t x = min_x; x <= max_x && index < MAX_PATH_POINTS; x += step) {
        tracking_data.path_points[index].x = x;
        tracking_data.path_points[index].y = min_y;
        printf("Path point %d: (%d, %d) - Top edge\r\n", index, x, min_y);
        index++;
    }

    // 3. 右边（从上到下）：右上 -> 右下
    for (uint16_t y = min_y; y <= max_y && index < MAX_PATH_POINTS; y += step) {
        tracking_data.path_points[index].x = max_x;
        tracking_data.path_points[index].y = y;
        printf("Path point %d: (%d, %d) - Right edge\r\n", index, max_x, y);
        index++;
    }

    // 4. 下边（从右到左）：右下 -> 左下
    for (int x = max_x; x >= (int)min_x && index < MAX_PATH_POINTS; x -= step) {
        tracking_data.path_points[index].x = x;
        tracking_data.path_points[index].y = max_y;
        printf("Path point %d: (%d, %d) - Bottom edge\r\n", index, x, max_y);
        index++;
        if (x <= (int)min_x + step) break;  // 防止无限循环
    }

    tracking_data.path_count = index;
    tracking_data.path_ready = true;

    printf("Rectangle path generated with %d points\r\n", tracking_data.path_count);
    printf("Path generation completed, ready for tracking\r\n");
}

/**
 * @brief 坐标转换为舵机角度
 */
float coordinate_to_servo_angle(uint16_t coord, bool is_x_axis)
{
    if (is_x_axis) {
        // X坐标转换为X轴舵机角度 (0-640 -> 0-270度)
        float ratio = (float)coord / COORD_X_MAX;
        return SERVO_X_MIN + ratio * (SERVO_X_MAX - SERVO_X_MIN);
    } else {
        // Y坐标转换为Y轴舵机角度 (0-480 -> 0-180度)
        float ratio = (float)coord / COORD_Y_MAX;
        return SERVO_Y_MIN + ratio * (SERVO_Y_MAX - SERVO_Y_MIN);
    }
}

/**
 * @brief 舵机控制
 */
void servo_control(float x_angle, float y_angle)
{
    printf("=== Servo Control ===\r\n");
    printf("Input angles: X=%.1f, Y=%.1f\r\n", x_angle, y_angle);
    
    // 限幅
    if (x_angle < SERVO_X_MIN) x_angle = SERVO_X_MIN;
    if (x_angle > SERVO_X_MAX) x_angle = SERVO_X_MAX;
    if (y_angle < SERVO_Y_MIN) y_angle = SERVO_Y_MIN;
    if (y_angle > SERVO_Y_MAX) y_angle = SERVO_Y_MAX;
    
    printf("Limited angles: X=%.1f, Y=%.1f\r\n", x_angle, y_angle);
    
    // 设置舵机角度
    printf("Setting X servo to %.1f degrees\r\n", x_angle);
    s20_set_angle(x_angle, SERVO_X_MAX);
    printf("Setting Y servo to %.1f degrees\r\n", y_angle);
    s20_set_angle(y_angle, SERVO_Y_MAX);
    
    printf("Servo control completed\r\n");
}

/**
 * @brief 跟踪控制
 */
void tracking_control(void)
{
    printf("=== Tracking Control Debug ===\r\n");
    printf("Path ready: %s, Laser data ready: %s\r\n", 
           tracking_data.path_ready ? "Yes" : "No", 
           laser_data_ready ? "Yes" : "No");
    
    if (!tracking_data.path_ready || !laser_data_ready) {
        printf("Skipping tracking control\r\n");
        return;
    }
    
    // 获取当前目标点
    point_t target = tracking_data.path_points[current_target_index];
    printf("Current target index: %d/%d\r\n", current_target_index, tracking_data.path_count);
    printf("Target point: (0x%04X, 0x%04X) = (%d, %d)\r\n", 
           target.x, target.y, target.x, target.y);
    printf("Current laser: (0x%04X, 0x%04X) = (%d, %d)\r\n", 
           current_laser_pos.x, current_laser_pos.y, current_laser_pos.x, current_laser_pos.y);
    
    // 计算当前激光点与目标点的位置误差
    float error_x = (float)target.x - (float)current_laser_pos.x;
    float error_y = (float)target.y - (float)current_laser_pos.y;
    printf("Position Error: (%.1f, %.1f) pixels\r\n", error_x, error_y);

    // 使用PID控制计算舵机角度调整量
    // PID的目标是让位置误差为0，输出为角度调整量
    float angle_adjustment_x = pid_calculate(&pid_x, 0.0f, error_x);  // 目标误差为0
    float angle_adjustment_y = pid_calculate(&pid_y, 0.0f, error_y);
    printf("Angle adjustments: X=%.2f°, Y=%.2f°\r\n", angle_adjustment_x, angle_adjustment_y);

    // 计算新的舵机角度（当前角度 + 调整量）
    float new_servo_x_angle = current_servo_x_angle + angle_adjustment_x;
    float new_servo_y_angle = current_servo_y_angle + angle_adjustment_y;

    // 限制舵机角度范围
    if (new_servo_x_angle < SERVO_X_MIN) new_servo_x_angle = SERVO_X_MIN;
    if (new_servo_x_angle > SERVO_X_MAX) new_servo_x_angle = SERVO_X_MAX;
    if (new_servo_y_angle < SERVO_Y_MIN) new_servo_y_angle = SERVO_Y_MIN;
    if (new_servo_y_angle > SERVO_Y_MAX) new_servo_y_angle = SERVO_Y_MAX;

    printf("Current servo angles: X=%.1f°, Y=%.1f°\r\n", current_servo_x_angle, current_servo_y_angle);
    printf("New servo angles: X=%.1f°, Y=%.1f°\r\n", new_servo_x_angle, new_servo_y_angle);

    // 控制舵机到新角度
    printf("Controlling servo to new angles...\r\n");
    servo_control(new_servo_x_angle, new_servo_y_angle);

    // 更新当前舵机角度状态
    current_servo_x_angle = new_servo_x_angle;
    current_servo_y_angle = new_servo_y_angle;
    printf("Servo control completed\r\n");
    
    // 检查是否到达目标点
    float distance = sqrtf(error_x * error_x + error_y * error_y);
    printf("Distance to target: %.1f pixels\r\n", distance);

    if (distance < 20.0f) {  // 增大容差到20像素
        current_target_index++;
        printf("=== Target Reached ===\r\n");
        printf("Reached point %d/%d, distance: %.1f\r\n", current_target_index, tracking_data.path_count, distance);
        printf("Moving to next target...\r\n");

        // 检查是否完成一圈
        if (current_target_index >= tracking_data.path_count) {
            path_completed = true;
            tracking_active = false;
            printf("Path completed! Tracking stopped.\r\n");
            return;
        }
    }
    
    printf("Tracking: target(0x%04X,0x%04X)=(%d,%d) laser(0x%04X,0x%04X)=(%d,%d) error(%.1f,%.1f) servo(%.1f,%.1f)\r\n",
           target.x, target.y, target.x, target.y,
           current_laser_pos.x, current_laser_pos.y, current_laser_pos.x, current_laser_pos.y,
           error_x, error_y, current_servo_x_angle, current_servo_y_angle);
}

/**
 * @brief 开始跟踪
 */
void start_tracking(void)
{
    if (tracking_data.path_ready) {
        tracking_active = true;
        current_target_index = 0;
        printf("Tracking started\r\n");
    }
}

/**
 * @brief 停止跟踪
 */
void stop_tracking(void)
{
    tracking_active = false;
    printf("Tracking stopped\r\n");
}

/**
 * @brief 重置舵机到中心位置
 */
void reset_servo_position(void)
{
    current_servo_x_angle = 135.0f;  // X轴中位
    current_servo_y_angle = 90.0f;   // Y轴中位

    printf("Resetting servo to center position...\r\n");
    servo_control(current_servo_x_angle, current_servo_y_angle);
    printf("Servo reset completed: X=%.1f°, Y=%.1f°\r\n", current_servo_x_angle, current_servo_y_angle);
}

/**
 * @brief 处理纯十六进制字符串格式的数据包（如"AA 00 4E..."）
 */
void process_hex_string_packet(uint8_t* data, uint16_t size)
{
    printf("Processing hex string packet\r\n");

    // 将十六进制字符串转换为二进制数据
    uint8_t binary_data[32];
    uint16_t binary_size = 0;

    uint16_t i = 0;
    while (i < size && binary_size < 32) {
        // 跳过空格、回车、换行
        if (data[i] == ' ' || data[i] == '\r' || data[i] == '\n') {
            i++;
            continue;
        }

        // 解析两个十六进制字符
        if (i + 1 < size) {
            uint8_t high = hex_char_to_byte(data[i]);
            uint8_t low = hex_char_to_byte(data[i + 1]);

            if (high != 0xFF && low != 0xFF) {
                binary_data[binary_size++] = (high << 4) | low;
                i += 2;
            } else {
                // 如果遇到无效字符，跳过
                i++;
            }
        } else {
            break;
        }
    }

    printf("Converted to binary (%d bytes): ", binary_size);
    for (int j = 0; j < binary_size; j++) {
        printf("%02X ", binary_data[j]);
    }
    printf("\r\n");

    // 直接处理转换后的二进制数据，避免递归调用
    process_binary_data(binary_data, binary_size);
}

/**
 * @brief 处理ASCII格式的数据包（以"0x"开头）
 */
void process_ascii_packet(uint8_t* data, uint16_t size)
{
    printf("Processing 0x-prefixed ASCII packet\r\n");

    // 将ASCII字符串转换为二进制数据
    uint8_t binary_data[32];
    uint16_t binary_size = 0;

    // 跳过"0x"前缀
    uint16_t i = 2;
    while (i < size && binary_size < 32) {
        // 跳过空格
        if (data[i] == ' ') {
            i++;
            continue;
        }

        // 解析两个十六进制字符
        if (i + 1 < size) {
            uint8_t high = hex_char_to_byte(data[i]);
            uint8_t low = hex_char_to_byte(data[i + 1]);

            if (high != 0xFF && low != 0xFF) {
                binary_data[binary_size++] = (high << 4) | low;
            }

            i += 2;
        } else {
            break;
        }
    }

    printf("Converted to binary: ");
    for (int j = 0; j < binary_size; j++) {
        printf("%02X ", binary_data[j]);
    }
    printf("\r\n");

    // 直接处理转换后的二进制数据，避免递归调用
    process_binary_data(binary_data, binary_size);
}

/**
 * @brief 处理二进制数据（避免递归调用process_received_data）
 */
void process_binary_data(uint8_t* data, uint16_t size)
{
    printf("Processing binary data: size=%d\r\n", size);

    if (size < 6) {
        printf("Binary packet too short\r\n");
        return;
    }

    // 检查帧头
    if (data[0] != FRAME_HEADER) {
        printf("Invalid frame header in binary data: %02X\r\n", data[0]);
        return;
    }

    // 根据数据包长度判断类型
    if (size == CORNERS_PACKET_LEN && data[17] == FRAME_TAIL) {
        printf("Processing corners packet from binary data\r\n");
        // 角点坐标包
        process_corners_packet(data);
    }
    else if (size == LASER_PACKET_LEN && data[5] == FRAME_TAIL) {
        printf("Processing laser packet from binary data\r\n");
        // 激光点坐标包
        process_laser_packet(data);
    }
    else {
        printf("Unknown binary packet type: size=%d, header=%02X, tail=%02X\r\n",
               size, data[0], data[size-1]);
    }
}

/**
 * @brief 将十六进制字符转换为字节
 */
uint8_t hex_char_to_byte(uint8_t c)
{
    if (c >= '0' && c <= '9') {
        return c - '0';
    } else if (c >= 'A' && c <= 'F') {
        return c - 'A' + 10;
    } else if (c >= 'a' && c <= 'f') {
        return c - 'a' + 10;
    }
    return 0xFF; // 无效字符
}

/**
 * @brief 测试循迹系统（调试用）
 */
void test_tracking_system(void)
{
    printf("=== Testing Tracking System ===\r\n");

    // 重置舵机到中心位置
    reset_servo_position();

    // 模拟角点坐标（一个标准矩形）
    tracking_data.corners[0].x = 200; tracking_data.corners[0].y = 150;  // 左上
    tracking_data.corners[1].x = 440; tracking_data.corners[1].y = 150;  // 右上
    tracking_data.corners[2].x = 440; tracking_data.corners[2].y = 330;  // 右下
    tracking_data.corners[3].x = 200; tracking_data.corners[3].y = 330;  // 左下

    printf("Test corners set:\r\n");
    for (int i = 0; i < 4; i++) {
        printf("Corner %d: (%d, %d)\r\n", i+1, tracking_data.corners[i].x, tracking_data.corners[i].y);
    }

    // 生成路径
    generate_rectangle_path();

    // 模拟激光点位置（偏离第一个路径点）
    current_laser_pos.x = 320;  // 中心位置，需要向左移动到路径起点
    current_laser_pos.y = 240;  // 中心位置，需要向下移动到路径起点
    laser_data_ready = true;

    printf("Test laser position: (%d, %d)\r\n", current_laser_pos.x, current_laser_pos.y);
    printf("First path point: (%d, %d)\r\n",
           tracking_data.path_points[0].x, tracking_data.path_points[0].y);

    // 开始跟踪
    if (tracking_data.path_ready) {
        tracking_active = true;
        current_target_index = 0;
        printf("Test tracking started\r\n");

        // 执行几次跟踪控制，模拟连续控制过程
        for (int i = 0; i < 3; i++) {
            printf("\n--- Control Step %d ---\r\n", i+1);
            tracking_control();

            // 模拟激光点逐渐接近目标（简化模拟）
            if (i < 2) {
                current_laser_pos.x += (tracking_data.path_points[0].x - current_laser_pos.x) * 0.3f;
                current_laser_pos.y += (tracking_data.path_points[0].y - current_laser_pos.y) * 0.3f;
                printf("Simulated new laser position: (%d, %d)\r\n",
                       current_laser_pos.x, current_laser_pos.y);
            }
        }
    }

    printf("=== Test Completed ===\r\n");
}



