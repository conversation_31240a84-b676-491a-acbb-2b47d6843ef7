#ifndef __PID_CONTROLLER_H
#define __PID_CONTROLLER_H

#include <stdint.h>
#include <stdbool.h>
#include "data_precess.h"

// PID结构体定义和函数声明
typedef struct {
    float kp, ki, kd;
    float integral;
    float last_error;
    float output;
    float min_output, max_output;
} PID_Controller_t;

void PID_Init(PID_Controller_t *pid, float kp, float ki, float kd, float min_out, float max_out);
float PID_Calculate(float target, float feedback, PID_Controller_t *pid);
void process_uart_data(uint8_t *data, uint16_t len);
int find_nearest_path_point(Point_t laser, Point_t *path, int n);
float step_limit(float output, float step);
void follow_path(void);

#endif










