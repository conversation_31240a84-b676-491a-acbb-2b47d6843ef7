#ifndef __LASER_TRACKING_H
#define __LASER_TRACKING_H

#include "bsp_system.h"

// 数据结构定义
typedef struct {
    uint16_t x;
    uint16_t y;
} point_t;

typedef struct {
    point_t corners[4];  // 4个角点
    point_t path_points[200];  // 路径点数组
    uint16_t path_count;  // 路径点数量
    bool path_ready;  // 路径是否已生成
} tracking_data_t;

typedef struct {
    float kp;  // 比例系数
    float ki;  // 积分系数
    float kd;  // 微分系数
    float error;  // 当前误差
    float last_error;  // 上次误差
    float integral;  // 积分项
    float output;  // 输出值
} pid_controller_t;

// 全局变量声明
extern tracking_data_t tracking_data;
extern pid_controller_t pid_x, pid_y;
extern point_t current_laser_pos;
extern bool laser_data_ready;
extern bool path_completed;

// 函数声明
void laser_tracking_init(void);
void process_received_data(uint8_t* data, uint16_t size);
void generate_rectangle_path(void);
void update_laser_position(uint16_t x, uint16_t y);
void tracking_control(void);
void pid_init(pid_controller_t* pid, float kp, float ki, float kd);
float pid_calculate(pid_controller_t* pid, float setpoint, float feedback);
float coordinate_to_servo_angle(uint16_t coord, bool is_x_axis);
void servo_control(float x_angle, float y_angle);
void start_tracking(void);
void stop_tracking(void);
void reset_servo_position(void);
void process_corners_packet(uint8_t* data);
void process_laser_packet(uint8_t* data);
void process_ascii_packet(uint8_t* data, uint16_t size);
void process_hex_string_packet(uint8_t* data, uint16_t size);
void process_binary_data(uint8_t* data, uint16_t size);
bool is_hex_string_format(uint8_t* data, uint16_t size);
uint8_t hex_char_to_byte(uint8_t c);
void test_tracking_system(void);

// 数据包类型
#define PACKET_TYPE_CORNERS 0x01  // 角点坐标包
#define PACKET_TYPE_LASER   0x02  // 激光点坐标包

// 帧标识
#define FRAME_HEADER 0xAA
#define FRAME_TAIL   0x55

// 数据包长度
#define CORNERS_PACKET_LEN 18  // 0xAA + 16字节坐标 + 0x55
#define LASER_PACKET_LEN   6   // 0xAA + 4字节坐标 + 0x55

// PID参数（针对像素误差到角度调整的控制）
// X轴：640像素对应270度，比例约为0.42度/像素
// Y轴：480像素对应180度，比例约为0.375度/像素
#define PID_X_KP 0.1f   // 比例系数：像素误差转角度调整
#define PID_X_KI 0.001f // 积分系数：消除稳态误差
#define PID_X_KD 0.02f  // 微分系数：抑制超调

#define PID_Y_KP 0.1f   // Y轴比例系数
#define PID_Y_KI 0.001f // Y轴积分系数
#define PID_Y_KD 0.02f  // Y轴微分系数

// 循迹参数
#define TRACKING_STEP_ANGLE 0.5f  // 每0.5度一个路径点
#define MAX_PATH_POINTS 200       // 最大路径点数

#endif






