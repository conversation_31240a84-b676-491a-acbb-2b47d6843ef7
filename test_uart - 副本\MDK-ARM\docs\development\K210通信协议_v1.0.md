# K210与STM32通信协议文档

## 1. 协议概述

| 项目 | 内容 |
|------|------|
| 通信接口 | UART3 (STM32) ↔ K210 |
| 波特率 | 115200 |
| 数据位 | 8位 |
| 停止位 | 1位 |
| 校验位 | 无 |
| 流控制 | 无 |

## 2. 通信协议设计

### 2.1 协议格式
所有指令以ASCII字符串形式传输，以`\r\n`结尾。

### 2.2 数据流向
- **STM32 → K210**: 控制指令、状态查询
- **K210 → STM32**: 轨迹数据、位置反馈、状态响应

## 3. 协议指令详述

### 3.1 STM32发送给K210的指令

#### 3.1.1 请求轨迹初始化
**指令格式**: `REQ_INIT\r\n`
**功能**: 请求K210发送黑框矩形的轨迹点坐标
**触发条件**: 用户发送`S4`指令启动K210轨迹循迹任务
**K210响应**: 发送`INIT:`协议数据

#### 3.1.2 开始循迹指令
**指令格式**: `START_TRACK\r\n`
**功能**: 开始执行轨迹循迹
**K210响应**: `ACK:TRACK_START\r\n`

#### 3.1.3 停止循迹指令
**指令格式**: `STOP_TRACK\r\n`
**功能**: 停止当前循迹任务
**K210响应**: `ACK:TRACK_STOP\r\n`

### 3.2 K210发送给STM32的数据

#### 3.2.1 轨迹初始化数据
**协议格式**: 
```
INIT:P1x123y456,P2x789y012,P3x345y678,P4x901y234,END\r\n
```

**字段说明**:
- `INIT:` - 协议头，标识轨迹初始化数据
- `P1x123y456` - 第1个轨迹点，x坐标123，y坐标456
- `P2x789y012` - 第2个轨迹点，x坐标789，y坐标012
- `P3x345y678` - 第3个轨迹点，x坐标345，y坐标678
- `P4x901y234` - 第4个轨迹点，x坐标901，y坐标234
- `END` - 数据结束标识

**循迹顺序**: 按照P1→P2→P3→P4→P1的顺序循环
**坐标系**: 像素坐标系，范围0-640(X轴)，0-480(Y轴)

**STM32响应**:
- 成功: `ACK:INIT_OK\r\n`
- 失败: `NAK:INIT_FAIL\r\n`

#### 3.2.2 实时位置数据
**协议格式**: 
```
POS:x123y456\r\n
```

**字段说明**:
- `POS:` - 协议头，标识当前位置数据
- `x123` - 当前X坐标123
- `y456` - 当前Y坐标456

**发送频率**: 30Hz (每33ms发送一次)
**坐标系**: 像素坐标系，与轨迹点坐标系一致

#### 3.2.3 状态响应
**成功响应**:
- `ACK:INIT_OK\r\n` - 轨迹初始化成功
- `ACK:TRACK_START\r\n` - 开始循迹成功
- `ACK:TRACK_STOP\r\n` - 停止循迹成功

**错误响应**:
- `NAK:INIT_FAIL\r\n` - 轨迹初始化失败
- `NAK:TRACK_BUSY\r\n` - 系统忙，无法执行指令

## 4. 状态机设计

### 4.1 STM32状态机
```
TRACK_IDLE (空闲)
    ↓ 接收INIT:数据
TRACK_INIT_RECEIVED (已接收初始化)
    ↓ 接收POS:数据或START_TRACK指令
TRACK_RUNNING (正在循迹)
    ↓ 接收STOP_TRACK指令
TRACK_COMPLETED (循迹完成)
    ↓ 自动回到TRACK_RUNNING继续循环
```

### 4.2 循迹控制逻辑
1. **目标点选择**: 按照P1→P2→P3→P4→P1的顺序
2. **到达判断**: 当前位置与目标点距离<10像素时切换到下一个点
3. **角度转换**: 像素坐标转换为舵机角度
   - X轴: `angle_x = pixel_x * 270.0 / 640.0`
   - Y轴: `angle_y = pixel_y * 180.0 / 480.0`
4. **舵机控制**: 调用`servo_set_angle()`函数控制舵机

## 5. 使用示例

### 5.1 完整循迹流程
```
1. 用户发送: S4
2. STM32发送: REQ_INIT\r\n
3. K210响应: INIT:P1x100y100,P2x500y100,P3x500y400,P4x100y400,END\r\n
4. STM32响应: ACK:INIT_OK\r\n
5. K210开始发送: POS:x123y456\r\n (30Hz频率)
6. STM32根据位置数据控制舵机循迹
7. 激光笔按照 左下→左上→右上→右下→左下 的顺序循迹
```

### 5.2 矩形轨迹示例
假设黑框矩形的四个顶点坐标为:
- 左下角 (100, 400) - P1
- 左上角 (100, 100) - P2  
- 右上角 (500, 100) - P3
- 右下角 (500, 400) - P4

K210发送的初始化数据:
```
INIT:P1x100y400,P2x100y100,P3x500y100,P4x500y400,END\r\n
```

循迹顺序: P1→P2→P3→P4→P1 (左下→左上→右上→右下→左下)

## 6. 错误处理

### 6.1 通信错误
- **数据格式错误**: STM32忽略格式不正确的数据
- **坐标超限**: 自动限制在有效范围内(0-640, 0-480)
- **通信超时**: 如果5秒内未收到K210数据，自动停止循迹

### 6.2 系统错误
- **轨迹点过多**: 最多支持20个轨迹点，超出部分忽略
- **内存不足**: 返回NAK:INIT_FAIL响应
- **舵机故障**: 记录错误日志，继续尝试控制

## 7. 调试接口

### 7.1 调试信息输出
STM32通过UART1输出调试信息:
```
Starting K210 Track Sequence Task !!!
Track points received: 4
Current target: P1(100,400)
Laser position: (123,456)
Servo angles: X=52.3°, Y=168.7°
```

### 7.2 手动测试指令
通过UART1发送测试指令:
- `S4` - 启动K210循迹任务
- `T` - 停止所有任务
- `FHT` - 暂停/恢复任务

## 8. 性能指标

| 指标项目 | 目标值 | 实际值 |
|---------|--------|--------|
| 位置更新频率 | 30Hz | 30Hz |
| 循迹精度 | ±10像素 | ±10像素 |
| 响应延迟 | <50ms | <33ms |
| 通信成功率 | >99% | 待测试 |
| 循迹完整性 | 100% | 待测试 |

## 9. 版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| v1.0 | 2025-01-24 | 初始版本，支持基本循迹功能 | Alex |

---

**文档状态**: 已完成  
**实现状态**: 代码已实现，待测试验证