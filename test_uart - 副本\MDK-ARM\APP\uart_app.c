#include "bsp_system.h"
#include "laser_tracking.h"

uint8_t uart1_rx_dma_buf[128];
uint8_t uart1_rx_buf[128];
uint8_t uart_rx_buf[8];



float Task1_point_angle[5][2] = {0};//任务1的5个点的角度
uint8_t i = 0;//任务1的点i
float error_pulse[5][2] = {0};//任务1的5个点两点间的误差
float prev_pulse[5][2] = {0};
bool uart1_rx_flag = false;
bool uart3_rx_flag = false;


int fputc(int ch, FILE *f)
{
    HAL_UART_Transmit(&huart3, (uint8_t *)&ch, 1, 0xFFFF);
    return ch;
}

uint32_t angle_to_pulse(float angle, bool is_x_axis) {
    // 确定角度范围
    float min_angle = 0.0f;
    float max_angle = is_x_axis ? 270.0f : 180.0f;
    
    // 限幅保护舵机
    if (angle < min_angle) angle = min_angle;
    if (angle > max_angle) angle = max_angle;
    
    // 计算脉冲宽度（单位：us）
    float pulse_us;
    if (is_x_axis) {
        pulse_us = 500.0f + (angle / 270.0f) * 2000.0f; // X轴：0-270度转500-2500us
    } else {
        pulse_us = 500.0f + (angle / 180.0f) * 2000.0f; // Y轴：0-180度转500-2500us
    }
    
    // 将脉冲宽度转换为定时器计数值（假设定时器时钟为72MHz，预分频72，则每微秒1个计数）
    return (uint32_t)(pulse_us);  // 1us = 1 tick
}


/**
 * @brief  设置S20舵机角度（支持浮点度数）
 * @param  angle: 目标角度（0.0f ~ 270.0f）
 * 
 * */ 

void s20_set_angle(float angle,float max_angle)
{
    // 约束输入角度在有效范围内
    if(angle < 0.0f) angle = 0.0f;
    if(angle > max_angle) angle = max_angle;

    // 计算比例因子 (0.0 - 1.0)
    float ratio = angle / max_angle;

    //脉冲宽度=500us+（angle/max_angle）*(2500-500)us
    uint32_t pulse_width = 500 + (uint32_t)((angle / max_angle) * (2500 - 500));
    __HAL_TIM_SET_COMPARE((max_angle == 180.0f ? &htim3 : &htim2), TIM_CHANNEL_1, pulse_width);
	
	// 更新当前角度变量
    current_servo_angle[(max_angle ==180.0f) ? 1 : 0] = angle;
}


void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
	if(huart->Instance == USART3)
	{
		printf("UART3 received %d bytes\r\n", Size);
		
		// 复制接收到的数据到处理缓冲区
		for(uint8_t i = 0; i<Size && i<sizeof(uart1_rx_buf); i++)
		{
			uart1_rx_buf[i] = uart1_rx_dma_buf[i];
		}
		// 确保字符串结束符
		uart1_rx_buf[Size < sizeof(uart1_rx_buf) ? Size : sizeof(uart1_rx_buf)-1] = '\0';
		
	uart1_rx_flag = true;	
		
		// 处理激光循迹数据
		process_received_data(uart1_rx_dma_buf, Size);
	}
	
	// 清除DMA缓冲区
	memset(uart1_rx_dma_buf,0,sizeof(uart1_rx_dma_buf));

    // 重新启动DMA接收
    HAL_UARTEx_ReceiveToIdle_DMA(&huart3, uart1_rx_dma_buf, sizeof(uart1_rx_dma_buf));
}

void uart1_task(void)
{
    if(uart1_rx_flag == false)
        return;

    uart1_rx_flag = false;

    // 处理X轴指令
    if(uart1_rx_buf[0] == 'x')
    {
        // 解析+号或-号后面的数字到X_Move_Angle
        sscanf((char*)&uart1_rx_buf[2], "%f", &X_Move_Angle);
        printf("X_Move_Angle = %f\r\n", X_Move_Angle);
        if(uart1_rx_buf[1] == '+')  // 左移
        {
            s20_set_angle(current_servo_angle[0] + X_Move_Angle, 270); // X轴最大角度应为270
            printf("X current_servo_angle = %f\r\n", current_servo_angle[0]);
        }
        else if(uart1_rx_buf[1] == '-')  // 右移
        {
            s20_set_angle(current_servo_angle[0] - X_Move_Angle, 270); // X轴最大角度应为270
            printf("X current_servo_angle = %f\r\n", current_servo_angle[0]);
        }
    }
    // 处理Y轴指令
    else if(uart1_rx_buf[0] == 'y')
    {
        sscanf((char*)&uart1_rx_buf[2], "%f", &Y_Move_Angle);
        if(uart1_rx_buf[1] == '+')  // 上移
        {
            s20_set_angle(current_servo_angle[1] + Y_Move_Angle, 180); // Y轴最大角度应为180
            printf("Y current_servo_angle = %f\r\n", current_servo_angle[1]);
        }
        else if(uart1_rx_buf[1] == '-')  // 下移
        {
            s20_set_angle(current_servo_angle[1] - Y_Move_Angle, 180); // Y轴最大角度应为180
            printf("Y current_servo_angle = %f\r\n", current_servo_angle[1]);
        }
    }
    else if(uart1_rx_buf[0] == 'B')
    {
        if(i < 5) { // 添加边界检查
            Task1_point_angle[i][0] = current_servo_angle[0];
            Task1_point_angle[i][1] = current_servo_angle[1];
            printf("Point %d recorded: X=%.1f, Y=%.1f\r\n", i, Task1_point_angle[i][0], Task1_point_angle[i][1]);
            i++;
        } else {
            printf("Error: Maximum 5 points allowed\r\n");
        }
    }
    else if(uart1_rx_buf[0] == 'S' && uart1_rx_buf[1] == '1')
    {
        printf("Executing Task1 with %d points\r\n", i);

        // 简化的任务执行：依次移动到记录的各个点
        for(uint8_t point = 0; point < i && point < 5; point++) {
            printf("Moving to point %d: X=%.1f, Y=%.1f\r\n", point,
                   Task1_point_angle[point][0], Task1_point_angle[point][1]);
            s20_set_angle(Task1_point_angle[point][0], 270);
            s20_set_angle(Task1_point_angle[point][1], 180);
            HAL_Delay(1000);
        }

        // 重置点计数器
        i = 0;
        printf("Task1 execution completed\r\n");
    }
		else if(uart1_rx_buf[0] == 'S' && uart1_rx_buf[1] == '2')
		{
						printf("will reset");
            s20_set_angle(90, 270);
            s20_set_angle(90, 180);
            // 同时重置循迹系统的舵机角度状态
            reset_servo_position();
            HAL_Delay(1000);
		}
		else if(uart1_rx_buf[0] == 'T' && uart1_rx_buf[1] == '1')
		{
			// 手动开始激光循迹（备用功能）
			if (tracking_data.path_ready && !path_completed) {
				start_tracking();
			} else {
				printf("Cannot start tracking: path not ready or already completed\r\n");
			}
		}
		else if(uart1_rx_buf[0] == 'T' && uart1_rx_buf[1] == '0')
		{
			// 手动停止激光循迹（备用功能）
			stop_tracking();
		}
		else if(uart1_rx_buf[0] == 'T' && uart1_rx_buf[1] == 'E' && uart1_rx_buf[2] == 'S' && uart1_rx_buf[3] == 'T')
		{
			// 测试循迹系统
			printf("Starting tracking system test...\r\n");
			test_tracking_system();
		}
		else if(uart1_rx_buf[0] == 'D' && uart1_rx_buf[1] == 'I' && uart1_rx_buf[2] == 'R')
		{
			// 测试舵机方向
			printf("Starting servo direction test...\r\n");
			test_servo_direction();
		}


    memset(uart1_rx_buf, 0, sizeof(uart1_rx_buf));  //清除缓存数据
}






// 串口接收回调
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart) 
	{
    // 假设每次接收一帧数据
	if(huart->Instance == USART3)
	{
		extern uint8_t uart_rx_buf[8];
    // 注释掉冲突的函数调用，使用laser_tracking的数据处理
    // process_uart_data(uart_rx_buf, 8);
	}
    // 重新启动接收
    HAL_UART_Receive_IT(huart, uart_rx_buf, 8);
}










