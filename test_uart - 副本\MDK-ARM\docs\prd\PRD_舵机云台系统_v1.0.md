# 产品需求文档 (PRD) - 舵机云台系统改造项目

## 1. 文档信息

| 项目名称 | 23年电赛E题前4问舵机云台系统 |
|---------|---------------------------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-01-24 |
| 负责人 | Emma (产品经理) |
| 审核人 | Mike (团队领袖) |
| 项目类型 | 硬件控制系统改造 |

## 2. 背景与问题陈述

### 2.1 项目背景
2023年全国大学生电子设计竞赛E题要求设计一个基于红色激光笔的精确定位控制系统。当前test_uart工程采用步进电机控制方案，存在响应速度慢、控制精度不足、机械噪音大等问题，无法满足电赛前4问的技术要求。

### 2.2 核心问题
- **响应速度问题**：步进电机启停时间长，影响实时控制性能
- **控制精度问题**：步进电机的步进角度限制了定位精度
- **系统复杂性**：步进电机需要复杂的脉冲控制和方向控制
- **功耗问题**：步进电机持续供电，功耗较高

### 2.3 解决方案概述
参考servo_master示例工程思路，将步进电机控制系统完全改造为舵机控制系统，利用舵机响应快、控制简单、精度高的特点，实现红色激光笔的精确二维定位控制。

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **系统改造目标**：完全替换步进电机控制，实现舵机控制系统
2. **性能提升目标**：提高响应速度50%以上，定位精度达到±1度
3. **兼容性目标**：保持现有通信协议和上层应用接口不变
4. **可靠性目标**：确保系统长时间稳定运行，满足电赛测试要求

### 3.2 关键结果 (Key Results)
- **KR1**：舵机角度控制精度达到±1度
- **KR2**：系统响应时间小于200ms
- **KR3**：激光笔定位稳定性达到±2像素
- **KR4**：串口通信成功率达到99.9%
- **KR5**：系统连续运行24小时无故障

### 3.3 反向指标 (Counter Metrics)
- 代码复杂度不得超过现有系统的120%
- 内存使用量不得超过现有系统的110%
- 功耗不得超过现有系统的90%

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**：参加电子设计竞赛的大学生团队
- **次要用户**：嵌入式系统开发工程师
- **维护用户**：系统集成和测试人员

### 4.2 用户故事
1. **作为竞赛参赛者**，我希望系统能够快速响应控制指令，以便在有限时间内完成测试任务
2. **作为系统操作者**，我希望通过串口发送简单指令就能控制激光笔精确定位
3. **作为测试人员**，我希望系统具有良好的稳定性和可重复性
4. **作为维护人员**，我希望代码结构清晰，便于调试和优化

## 5. 功能规格详述

### 5.1 核心功能模块

#### 5.1.1 舵机控制模块
**功能描述**：提供统一的舵机角度控制接口
**输入参数**：
- 轴向选择：X轴(0-270度) / Y轴(0-180度)
- 目标角度：浮点数，精度0.1度
**输出结果**：
- PWM信号：20ms周期，500-2500us脉宽
- 角度反馈：当前实际角度值
**异常处理**：
- 角度超限保护：自动限制在有效范围内
- 舵机过载保护：检测异常电流并停止输出

#### 5.1.2 PID控制模块
**功能描述**：实现闭环位置控制，提高定位精度
**控制参数**：
- Kp：比例系数，初始值0.15
- Ki：积分系数，初始值0.01  
- Kd：微分系数，初始值0.05
**控制策略**：
- 自适应参数调整：根据误差大小动态调整PID参数
- 死区控制：误差小于2度时停止调整，减少抖动
- 输出限制：单次角度变化不超过5度

#### 5.1.3 串口通信模块
**功能描述**：处理上位机和OpenMV的通信数据
**UART1指令格式**：
- 角度控制：`X+123` / `X-123` / `Y+456` / `Y-456`
- 任务控制：`S1` / `S2` / `S3` (启动任务) / `T` (停止)
- 状态查询：`OK` (确认) / `A` (记录坐标)
- 特殊指令：`FHT` (发挥题暂停)
**UART3数据格式**：
- 坐标数据：`GDx123y456` (当前位置)
- 矩形框数据：`JX x123y456 x789y012 x345y678 x901y234`

### 5.2 业务逻辑规则

#### 5.2.1 角度控制逻辑
```
IF 接收到角度控制指令 THEN
    解析轴向和角度值
    IF 角度值在有效范围内 THEN
        调用舵机控制接口
        更新当前角度状态
        返回执行成功
    ELSE
        限制角度到有效范围
        记录警告日志
    END IF
END IF
```

#### 5.2.2 PID控制逻辑
```
WHILE PID控制使能 DO
    读取当前位置反馈
    计算位置误差
    IF 误差绝对值 > 死区阈值 THEN
        计算PID输出
        应用自适应参数调整
        限制输出幅度
        发送角度控制指令
    END IF
    延时控制周期
END WHILE
```

### 5.3 边缘情况与异常处理

#### 5.3.1 硬件异常
- **舵机断线**：检测PWM输出无响应，切换到安全模式
- **电源不足**：监测供电电压，低于阈值时降低控制频率
- **过热保护**：检测到过热时暂停控制，等待冷却

#### 5.3.2 通信异常  
- **数据丢失**：超时重传机制，最多重试3次
- **格式错误**：忽略错误数据，记录错误日志
- **缓冲区溢出**：清空缓冲区，重新开始接收

#### 5.3.3 控制异常
- **角度超限**：自动限制到有效范围，不报错
- **PID发散**：检测到输出饱和时重置积分项
- **响应超时**：超过500ms无响应时切换到开环控制

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- ✅ 舵机PWM控制系统
- ✅ 统一的角度控制接口
- ✅ PID闭环控制算法
- ✅ 串口通信协议处理
- ✅ 系统初始化和配置
- ✅ 异常处理和安全保护
- ✅ 激光器控制协调

### 6.2 排除功能 (Out of Scope)
- ❌ 步进电机控制代码（完全移除）
- ❌ 新增通信协议（保持现有协议）
- ❌ 硬件电路设计（仅软件改造）
- ❌ 上位机软件开发
- ❌ OpenMV视觉算法优化
- ❌ 机械结构改动

## 7. 依赖与风险

### 7.1 内部依赖项
- **硬件依赖**：STM32F103微控制器，TIM2/TIM3定时器资源
- **软件依赖**：HAL库，现有的串口通信框架
- **接口依赖**：现有的任务调度系统，激光器控制接口

### 7.2 外部依赖项
- **硬件设备**：270度X轴舵机，180度Y轴舵机
- **测试设备**：示波器（验证PWM输出），万用表（检测电流）
- **开发工具**：Keil MDK-ARM，STM32CubeMX

### 7.3 潜在风险评估

| 风险项目 | 风险等级 | 影响描述 | 缓解措施 |
|---------|---------|----------|----------|
| 舵机响应特性不匹配 | 中等 | PID参数需要重新调优 | 预留充足的测试调优时间 |
| PWM配置错误 | 高 | 舵机无法正常工作 | 详细验证定时器配置参数 |
| 代码重构引入Bug | 中等 | 系统功能异常 | 分步骤重构，每步都进行测试 |
| 性能指标不达标 | 中等 | 无法满足电赛要求 | 建立性能基准测试 |
| 兼容性问题 | 低 | 与现有系统不兼容 | 保持接口一致性设计 |

## 8. 发布初步计划

### 8.1 开发阶段规划

#### 阶段1：基础架构搭建 (2天)
- 系统架构设计
- 定时器PWM配置
- 舵机控制接口开发

#### 阶段2：核心功能实现 (3天)  
- 步进电机代码清理
- 角度控制函数重构
- PID控制参数优化

#### 阶段3：系统集成测试 (2天)
- 串口通信验证
- 系统功能测试
- 性能指标验证

#### 阶段4：文档与交付 (1天)
- 技术文档编写
- 项目交付准备

### 8.2 测试策略
- **单元测试**：每个模块独立测试
- **集成测试**：模块间接口测试
- **系统测试**：完整功能验证
- **性能测试**：关键指标验证
- **稳定性测试**：长时间运行测试

### 8.3 验收标准
1. 所有功能模块通过单元测试
2. 系统集成测试100%通过
3. 性能指标达到设计要求
4. 代码质量符合规范
5. 技术文档完整准确

## 9. 版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| v1.0 | 2025-01-24 | 初始版本创建 | Emma |

---

**文档状态**：已完成  
**下一步行动**：提交技术架构设计评审