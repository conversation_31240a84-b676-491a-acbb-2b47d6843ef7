# 激光循迹算法修复报告

## 1. 问题分析

### 1.1 问题现象
- 数据识别正常，角点坐标和激光点坐标都能正确解析
- 但激光点没有按照黑框坐标进行循迹
- 舵机可能没有正确响应或移动到错误位置

### 1.2 问题根因分析

通过代码审查发现以下问题：

#### 问题1：坐标转换函数返回类型错误
```c
// 原代码
uint16_t coordinate_to_servo_angle(uint16_t coord, bool is_x_axis)

// 问题：返回整数类型，但PID控制需要浮点数精度
```

#### 问题2：PID控制逻辑不当
```c
// 原代码
float new_x_angle = current_x_angle + pid_output_x;
float new_y_angle = current_y_angle + pid_output_y;

// 问题：直接累加PID输出可能导致角度超出范围或震荡
```

#### 问题3：路径生成顺序可能不合理
- 原路径生成：上->右->下->左
- 可能与预期的循迹顺序不符

#### 问题4：到达判断容差过小
- 原容差：10像素
- 可能导致难以到达目标点

## 2. 修复方案

### 2.1 修复坐标转换函数
```c
// 修复后
float coordinate_to_servo_angle(uint16_t coord, bool is_x_axis)
{
    if (is_x_axis) {
        // X坐标转换为X轴舵机角度 (0-640 -> 0-270度)
        float ratio = (float)coord / COORD_X_MAX;
        return SERVO_X_MIN + ratio * (SERVO_X_MAX - SERVO_X_MIN);
    } else {
        // Y坐标转换为Y轴舵机角度 (0-480 -> 0-180度)
        float ratio = (float)coord / COORD_Y_MAX;
        return SERVO_Y_MIN + ratio * (SERVO_Y_MAX - SERVO_Y_MIN);
    }
}
```

**改进点**：
- 返回类型改为`float`，提供更高精度
- 添加详细注释说明坐标映射关系

### 2.2 简化控制逻辑
```c
// 修复后：直接使用目标角度控制
float target_x_angle = coordinate_to_servo_angle(target.x, true);
float target_y_angle = coordinate_to_servo_angle(target.y, false);

// 直接控制到目标位置
servo_control(target_x_angle, target_y_angle);
```

**改进点**：
- 暂时简化为直接位置控制，避免PID累积误差
- 后续可根据需要重新启用PID控制

### 2.3 优化路径生成算法
```c
// 修复后：按照左下->左上->右上->右下->左下的顺序
void generate_rectangle_path(void)
{
    // 1. 左边（从下到上）：左下 -> 左上
    for (uint16_t y = max_y; y >= min_y && index < MAX_PATH_POINTS; y -= step) {
        tracking_data.path_points[index].x = min_x;
        tracking_data.path_points[index].y = y;
        index++;
    }
    
    // 2. 上边（从左到右）：左上 -> 右上
    for (uint16_t x = min_x; x <= max_x && index < MAX_PATH_POINTS; x += step) {
        tracking_data.path_points[index].x = x;
        tracking_data.path_points[index].y = min_y;
        index++;
    }
    
    // 3. 右边（从上到下）：右上 -> 右下
    for (uint16_t y = min_y; y <= max_y && index < MAX_PATH_POINTS; y += step) {
        tracking_data.path_points[index].x = max_x;
        tracking_data.path_points[index].y = y;
        index++;
    }
    
    // 4. 下边（从右到左）：右下 -> 左下
    for (int x = max_x; x >= (int)min_x && index < MAX_PATH_POINTS; x -= step) {
        tracking_data.path_points[index].x = x;
        tracking_data.path_points[index].y = max_y;
        index++;
    }
}
```

**改进点**：
- 明确的循迹顺序，符合预期
- 增大步长到10像素，减少路径点数量
- 添加详细的调试输出
- 防止无限循环的保护机制

### 2.4 调整到达判断参数
```c
// 修复后
if (distance < 20.0f) {  // 增大容差到20像素
    current_target_index++;
    printf("=== Target Reached ===\r\n");
    printf("Reached point %d/%d, distance: %.1f\r\n", 
           current_target_index, tracking_data.path_count, distance);
}
```

**改进点**：
- 容差从10像素增加到20像素
- 增加详细的到达判断调试信息

## 3. 新增功能

### 3.1 测试函数
新增`test_tracking_system()`函数用于调试：
```c
void test_tracking_system(void)
{
    // 模拟角点坐标
    tracking_data.corners[0].x = 100; tracking_data.corners[0].y = 100;  // 左上
    tracking_data.corners[1].x = 500; tracking_data.corners[1].y = 100;  // 右上
    tracking_data.corners[2].x = 500; tracking_data.corners[2].y = 300;  // 右下
    tracking_data.corners[3].x = 100; tracking_data.corners[3].y = 300;  // 左下
    
    // 生成路径并测试循迹
    generate_rectangle_path();
    // ... 测试逻辑
}
```

### 3.2 测试指令
在uart_app.c中添加`TEST`指令：
```c
else if(uart1_rx_buf[0] == 'T' && uart1_rx_buf[1] == 'E' && 
        uart1_rx_buf[2] == 'S' && uart1_rx_buf[3] == 'T')
{
    printf("Starting tracking system test...\r\n");
    test_tracking_system();
}
```

## 4. 调试建议

### 4.1 分步测试流程

1. **发送TEST指令**测试基本功能
2. **发送角点坐标**验证路径生成
3. **发送激光点坐标**验证循迹控制
4. **观察串口输出**分析问题

### 4.2 关键调试信息

系统会输出以下调试信息：
```
=== Processing Corner Coordinates ===
Corner 1: (0x004E, 0x00A1) = (78, 161)
...
Rectangle bounds: X(78-217), Y(57-161)
Path point 0: (78, 161) - Left edge
...
Rectangle path generated with XX points

=== Tracking Control Debug ===
Current target index: 0/XX
Target point: (78, 161)
Current laser: (300, 240)
Target servo angles: X=32.9, Y=60.4
Distance to target: 245.2 pixels
Setting servo to target angles...
```

### 4.3 问题排查步骤

1. **确认路径生成**：检查路径点是否按预期顺序生成
2. **确认坐标转换**：验证坐标到角度的转换是否正确
3. **确认舵机控制**：检查舵机是否实际移动到目标角度
4. **确认到达判断**：观察距离计算和到达判断逻辑

## 5. 测试用例

### 5.1 基本功能测试
```
1. 发送: TEST
   预期: 系统执行内置测试，输出详细调试信息

2. 发送角点: AA 00 64 00 78 01 F4 00 78 01 F4 01 2C 00 64 01 2C 55
   预期: 生成矩形路径，输出路径点信息

3. 发送激光点: AA 00 96 00 96 55
   预期: 开始循迹，舵机移动到目标位置
```

### 5.2 循迹测试
```
持续发送不同位置的激光点坐标，观察舵机是否按照矩形路径移动
```

---

**修复状态**：✅ 已完成  
**测试状态**：待验证  
**负责人**：Alex (工程师)  
**完成时间**：2025-01-28
