# 舵机控制方向问题诊断报告

## 1. 问题现象分析

### 1.1 用户反馈
- **现象**：舵机只旋转了一下，没有按照矩形框路径进行循迹
- **调试信息显示**：控制逻辑在工作，但舵机移动方向可能错误

### 1.2 调试信息分析

从用户提供的调试信息：
```
Target point: (200, 330)
Current laser: (320, 240)
Position Error: (-120.0, 90.0) pixels
Angle adjustments: X=14.50°, Y=-10.89°
Current servo angles: X=135.0°, Y=90.0°
New servo angles: X=149.5°, Y=79.1°
```

**关键发现**：
1. **位置误差**：(-120, 90) 表示激光点需要向左移动120像素，向下移动90像素
2. **角度变化**：X轴角度从135°增大到149.5°，Y轴角度从90°减小到79.1°
3. **控制方向疑问**：如果激光点需要向左移动，X轴舵机角度应该减小还是增大？

## 2. 问题根因分析

### 2.1 PID调用参数错误 ✅ 已修复

**原问题**：
```c
float angle_adjustment_x = pid_calculate(&pid_x, 0.0f, error_x);
```

**问题分析**：
- 当error_x = -120时，PID内部计算：error = 0.0f - (-120.0f) = +120.0f
- 导致输出为正值，控制方向错误

**修复方案**：
```c
float angle_adjustment_x = pid_calculate(&pid_x, error_x, 0.0f);
```

### 2.2 舵机控制方向映射未确认 ⚠️ 需验证

**核心问题**：不确定舵机角度增大对应激光点向哪个方向移动

**需要验证的映射关系**：
- X轴舵机角度增大 → 激光点向左移动 or 向右移动？
- Y轴舵机角度增大 → 激光点向上移动 or 向下移动？

## 3. 测试验证方案

### 3.1 方向测试指令

**新增测试指令**：`DIR`

**测试流程**：
1. 舵机回到中心位置（X=135°, Y=90°）
2. X轴舵机角度增大20度（135° → 155°），观察激光点移动方向
3. X轴舵机角度减小20度（135° → 115°），观察激光点移动方向
4. Y轴舵机角度增大20度（90° → 110°），观察激光点移动方向
5. Y轴舵机角度减小20度（90° → 70°），观察激光点移动方向

**观察要点**：
- 记录每次角度变化对应的激光点移动方向
- 确定正确的控制方向映射关系

### 3.2 修复后的循迹测试

**测试指令**：`TEST`

**预期改进**：
- PID参数修复后，控制方向应该更合理
- 如果方向仍然错误，需要根据DIR测试结果进一步调整

## 4. 可能的修复方案

### 4.1 如果控制方向完全相反

**修复方案1**：反转PID输出
```c
float angle_adjustment_x = -pid_calculate(&pid_x, error_x, 0.0f);
float angle_adjustment_y = -pid_calculate(&pid_y, error_y, 0.0f);
```

**修复方案2**：反转误差计算
```c
float error_x = (float)current_laser_pos.x - (float)target.x;  // 反转
float error_y = (float)current_laser_pos.y - (float)target.y;  // 反转
```

### 4.2 如果只有某个轴方向错误

**修复方案**：单独反转对应轴
```c
float angle_adjustment_x = -pid_calculate(&pid_x, error_x, 0.0f);  // 只反转X轴
float angle_adjustment_y = pid_calculate(&pid_y, error_y, 0.0f);   // Y轴保持
```

### 4.3 如果控制幅度不够

**修复方案**：调整PID参数
```c
#define PID_X_KP 0.2f   // 增大比例系数
#define PID_X_KI 0.002f // 增大积分系数
#define PID_X_KD 0.05f  // 增大微分系数
```

## 5. 测试步骤

### 5.1 立即测试步骤

1. **编译并下载修复后的程序**
2. **发送DIR指令**，观察舵机方向测试：
   ```
   DIR
   ```
3. **记录观察结果**：
   - X轴舵机角度增大时，激光点向哪个方向移动？
   - Y轴舵机角度增大时，激光点向哪个方向移动？

4. **发送TEST指令**，测试修复后的循迹：
   ```
   TEST
   ```

### 5.2 根据测试结果调整

**如果DIR测试显示方向错误**：
- 应用对应的修复方案（反转控制方向）
- 重新测试循迹功能

**如果方向正确但幅度不够**：
- 调整PID参数，增大控制幅度
- 重新测试循迹功能

## 6. 预期结果

### 6.1 正确的控制效果

**理想情况**：
- 激光点应该平滑地向目标路径点移动
- 舵机角度应该渐进式调整
- 位置误差应该逐渐减小

**调试信息应该显示**：
```
Position Error: (-120.0, 90.0) pixels  → (0.0, 0.0) pixels
Angle adjustments: 合理的调整量
Servo angles: 向正确方向变化
Distance to target: 150.0 → 0.0 pixels
```

### 6.2 问题解决标志

✅ **激光点向目标点移动**：而不是远离目标点  
✅ **舵机连续调整**：而不是只动一下就停止  
✅ **误差逐渐减小**：距离目标点越来越近  
✅ **能够循迹**：沿着矩形路径移动  

## 7. 调试指令总结

- `DIR` - 测试舵机控制方向
- `TEST` - 测试修复后的循迹功能
- `S2` - 重置舵机到中心位置

---

**诊断状态**：✅ 已完成  
**修复状态**：🔄 部分修复，待验证  
**负责人**：Alex (工程师)  
**完成时间**：2025-01-28

**下一步**：请按照测试步骤验证修复效果，并根据结果进行进一步调整。
