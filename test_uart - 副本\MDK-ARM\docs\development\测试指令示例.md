# 数据协议测试指令示例

## 1. 角点坐标测试

### 1.1 测试数据说明
角点坐标包格式：`AA x0_H x0_L y0_H y0_L x1_H x1_L y1_H y1_L x2_H x2_L y2_H y2_L x3_H x3_L y3_H y3_L 55`

### 1.2 测试指令

#### 测试1：基本矩形角点
**发送指令**：
```
AA 00 4E 00 A1 00 D6 00 98 00 D9 00 39 00 44 00 38 55
```

**数据解析**：
- 帧头：AA
- 角点1：x=0x004E(78), y=0x00A1(161)
- 角点2：x=0x00D6(214), y=0x0098(152)  
- 角点3：x=0x00D9(217), y=0x0039(57)
- 角点4：x=0x0044(68), y=0x0038(56)
- 帧尾：55

**预期输出**：
```
Received data packet: size=35
Detected hex string format packet
Processing hex string packet
Converted to binary (18 bytes): AA 00 4E 00 A1 00 D6 00 98 00 D9 00 39 00 44 00 38 55
Processing binary data: size=18
Processing corners packet from binary data
=== Processing Corner Coordinates ===
Corner 1: (0x004E, 0x00A1) = (78, 161)
Corner 2: (0x00D6, 0x0098) = (214, 152)
Corner 3: (0x00D9, 0x0039) = (217, 57)
Corner 4: (0x0044, 0x0038) = (68, 56)
Rectangle path generated with XX points
Waiting for laser point to start tracking...
```

#### 测试2：标准640x480范围角点
**发送指令**：
```
AA 00 64 00 78 02 58 00 78 02 58 01 E0 00 64 01 E0 55
```

**数据解析**：
- 角点1：x=100, y=120
- 角点2：x=600, y=120
- 角点3：x=600, y=480
- 角点4：x=100, y=480

## 2. 激光点坐标测试

### 2.1 测试数据说明
激光点坐标包格式：`AA x_H x_L y_H y_L 55`

### 2.2 测试指令

#### 测试1：中心点位置
**发送指令**：
```
AA 01 40 00 F0 55
```

**数据解析**：
- 帧头：AA
- X坐标：0x0140(320)
- Y坐标：0x00F0(240)
- 帧尾：55

**预期输出**：
```
Received data packet: size=11
Detected hex string format packet
Processing hex string packet
Converted to binary (6 bytes): AA 01 40 00 F0 55
Processing binary data: size=6
Processing laser packet from binary data
Laser position updated: (0x0140, 0x00F0) = (320, 240)
Servo angles: X=135.0°, Y=90.0°
```

#### 测试2：左上角位置
**发送指令**：
```
AA 00 32 00 1E 55
```

**数据解析**：
- X坐标：0x0032(50)
- Y坐标：0x001E(30)

## 3. 兼容性测试

### 3.1 原有0x前缀格式（应该仍然工作）
**发送指令**：
```
0xAA 00 4E 00 A1 00 D6 00 98 00 D9 00 39 00 44 00 38 55
```

**预期输出**：
```
Detected 0x-prefixed ASCII format packet
Processing 0x-prefixed ASCII packet
```

### 3.2 二进制格式（应该仍然工作）
直接发送字节数据，预期正常处理。

## 4. 错误处理测试

### 4.1 无效帧头测试
**发送指令**：
```
BB 00 4E 00 A1 00 D6 00 98 00 D9 00 39 00 44 00 38 55
```

**预期输出**：
```
Invalid frame header in binary data: BB
```

### 4.2 无效帧尾测试
**发送指令**：
```
AA 00 4E 00 A1 00 D6 00 98 00 D9 00 39 00 44 00 38 66
```

**预期输出**：
```
Unknown binary packet type: size=18, header=AA, tail=66
```

### 4.3 数据包长度错误测试
**发送指令**：
```
AA 00 4E 00 A1 55
```

**预期输出**：
```
Unknown binary packet type: size=6, header=AA, tail=55
```

## 5. 完整测试流程

### 5.1 步骤1：发送角点坐标
```
AA 00 64 00 78 02 58 00 78 02 58 01 E0 00 64 01 E0 55
```

### 5.2 步骤2：发送激光点坐标开始循迹
```
AA 00 64 00 78 55
```

### 5.3 步骤3：持续发送激光点坐标
```
AA 00 6E 00 78 55
AA 00 78 00 78 55
AA 00 82 00 78 55
...
```

### 5.4 预期系统行为
1. 接收角点坐标后生成矩形路径
2. 接收第一个激光点坐标后自动开始循迹
3. 根据激光点位置计算PID控制输出
4. 控制舵机移动到目标位置
5. 完成一圈循迹后自动停止

## 6. 调试建议

1. **串口监控**：使用串口调试工具监控UART1输出的调试信息
2. **数据验证**：确认发送的十六进制字符串格式正确
3. **分步测试**：先测试角点坐标，再测试激光点坐标
4. **硬件检查**：确认舵机连接和PWM输出正常
5. **参数调整**：根据实际效果调整PID参数

---

**文档版本**：v1.0  
**创建时间**：2025-01-28  
**负责人**：Alex (工程师)
